import time
import json
import asyncio
from loguru import logger
from langchain_core.prompts import ChatPromptTemplate
from app.core import get_small_model
from app.services.recall.retrieval_utils import fix_json


async def summary(query, kgs,langfuse_handler):
    """
    异步处理知识图谱，进行筛选和总结。

    :params
        query: 用户查询
        kgs: 知识图谱列表，每个元素是包含 '\n来源：' 的字符串

    :return
        filter_index: 相关文档的索引列表
        filter_docs: 相关文档内容列表
        summary_docs: 总结内容列表
    """
    # 提取 kgs_content，与原始逻辑一致
    kgs_content = ["来源：" + i.split("\n来源：")[1] for i in kgs]

    # prompt 模板 - 注意这里不要使用 .format()，让 ChatPromptTemplate 来处理变量
    template = '''以下是检索到的文档: 

{context}

这是用户的问题: {query}

以下是要求：
- 输出格式为完整json
- 如果文档能够很有助于回答问题，输出yes，如果仅是部分相关，输出support，不相关输出no
- 如果输出yes或者support，则进行总结操作
- summary是总结操作，根据文档中的来源于内容，总结成一段内容
- 只对文档内容总结，不对问题做出推理
- 只输出一个字典，字典中有judege和summary两个key，judge是判断yes,support,no，summary是进行总结操作
- 不允许输出非字典的其他内容

以下是输出的例子：
如果文档非常助于回答问题，则按如下要求输出：
{{"judege":"yes", "summary":"检索到的文档和问题有哪些相关内容，有什么信息可以回答部分的问题，请做一个详细的分析"}}

如果文档有与问题部分相关，则按如下要求输出：
{{"judege":"support", "summary":"检索到的文档和问题有哪些相关内容，有什么信息可以回答部分的问题，请做一个详细的分析"}}

如果文档对回答问题没有帮助，按如下要求输出，summary对应一个空字符串：
{{"judege":"no", "summary":""}}'''

    system_prompt = "你是一个文档处理专家，善于判断用户的问题是否和文档相关，并且会做出相应的总结"

    # 创建模型实例
    model = get_small_model(temperature=0)

    # 定义处理单个 kg 的异步协程
    async def process_kg(idx, kg_content):
        """处理单个知识图谱条目，返回结果或 None"""
        try:
            # 创建 ChatPromptTemplate
            prompt = ChatPromptTemplate.from_messages([
                ("system", system_prompt),
                ("human", template)
            ])

            # 创建链并调用
            chain = prompt | model
            response = await chain.ainvoke({"context": kg_content, "query": query},config={"callbacks": [langfuse_handler]})

            logger.info(f"Response for document {idx}: {response.content}")

            # 解析响应 - 使用更安全的 JSON 解析
            try:
                # 先尝试修复 JSON 格式
                fixed_content = fix_json(response.content)
                if isinstance(fixed_content, str):
                    output = json.loads(fixed_content)
                else:
                    output = fixed_content
            except (json.JSONDecodeError, ValueError):
                print(f'无法解析响应内容：{response.content}')
                return None

            # 如果 judge 不是 'no'，返回索引、原始文档和总结
            if output.get('judege') != 'no':
                return idx, kgs[idx], output.get('summary', '')
            return None

        except Exception as e:
            # 处理异常，打印错误并跳过该条目
            print(f'处理文档 {idx} 时出错：{e}')
            return None

    # 创建协程列表，按 kgs_content 的顺序处理
    coroutines = [process_kg(idx, kg_content) for idx, kg_content in enumerate(kgs_content)]

    # 并发执行所有协程
    results = await asyncio.gather(*coroutines, return_exceptions=True)

    # 过滤掉 None 值和异常，只保留有效结果
    filtered_results = []
    for r in results:
        if r is not None and not isinstance(r, Exception):
            filtered_results.append(r)

    # 提取 filter_index, filter_docs, summary_docs
    filter_index = [r[0] for r in filtered_results]
    filter_docs = [r[1] for r in filtered_results]
    summary_docs = [r[2] for r in filtered_results]

    # 返回结果，与原始代码格式一致
    return filter_index, filter_docs, summary_docs


if __name__ == "__main__":
    input_text = '''
    1．在五脏之中，哪个脏器具备"刚脏"的特点？{'A': '肝', 'B': '肺', 'C': '脾', 'D': '心', 'E': '肾'}，请选择正确的选项，并解释为什么选择这个选项，其他选项不正确
    '''
    judege_list = [
        '\n来源：中医基础理论学--第一章 中医学的哲学基础--第三节 五行学说--三、五行学说在中医学中的运用--（二） 说明五脏功能及其关系--1.阐释五脏生理功能\n\n内容：中医学根据五行特性，取象比类，将五脏分别归属于五行。如肝气喜条达而恶抑郁，具有疏 通气血、调畅情志的功能，相应于木之生长、升发、条达的特性，故肝属木；心具有主血脉而推 动血液运行、主神明为脏腑之大主的功能，相应于火之温热、光明的特性，敌心属火：脾具有运 化水谷、化生精微、为气血生化之源以营养脏腑形体的功能，相应于土之生化万物的特性，故脾 属土；肺气肃降，具有主呼吸、通调水道输布水液的功能，相应于金之清肃、收敛的特性，故肺 属金；肾具有藏精、主水的功能，相应于水之滋润、下行、闭藏的特性，故肾属水。\n\n\n',
        '\n来源：中医基础理论学--第三章 藏象--第二节 五脏--四、肝--（一）生理特性--3.肝为刚脏\n\n内容：刚，刚强暴急之谓。肝具有刚强、躁急的生理特性。肝内寄相火，主升、主动，阳气用事， 故称"刚脏"。如《温病条辨·湿温》："肝为刚脏，内寄相火，非纯刚所能折。"肝气升动太过， 易于上亢、逆乱。临床上，肝病多见因阳亢、火旺、热极、阴虚而致肝气升动太过的病理变化， 如肝气上逆、肝火上炎、肝阳上亢和肝风内动等，从而出现眩晕、面赤、烦躁易怒、筋脉拘挛， 甚则抽搐、角弓反张等症状，也反证肝气的刚强躁急特性。治疗多用疏肝补虚、泻火滋阴、以柔 克刚等法，以合木之曲直特性。\n\n肝体阴而用阳，即肝主藏血，以血为体，血属阴；肝主疏泄，以气为用，气属阳。肝体阴 柔，其用阳刚，阴阳和调，刚柔相济。\n\n肝为刚脏与肺为娇脏相对而言，肝气主左升，肺气主右降，左升与右降相反相成。若肝气升 动太过，肺气肃降不及，则可出现"左升太过，右降不及"的肝火犯肺的病机变化。\n\n\n',
        '\n来源：中医基础理论学--第三章 藏象--第二节 五脏\n\n内容：五脏，即心、肺、脾、肝、肾的合称。五脏的共同生理特点是化生和贮藏精气，并能藏神而 称为"神脏"，又与时间、空间等环境因素密切相关。五脏虽各有所司，但彼此协调，共同维持 生命活动。\n\n\n',
        '\n来源：内经临床运用--第二章 明 阳--第三节素问·宣明五气--经文分析--（四）五脏所恶\n\n内容：恶，畏恶，指五脏发病的主因和病变的主要倾向而言。心恶热，既指温、暑、热之类的病邪易于伤及心，也反映了心的病变多偏热性，如热入心包则神昏谵妄，内陷营血则动血耗血，心火血热则多发疮疡，五志化火或痰火相挟可致心神狂乱等等。各种病邪都可伤肺，但以寒为首，即如《灵枢·邪气脏腑病形》所述："形寒寒饮则伤肺，以其两寒相感，中外皆伤，故气逆而上行。"故肺部感染以及慢支、哮喘等病每因寒而发。\n\n肝恶风, 乃指肝病易生风证的倾向, 故名肝风, 即如血虚生风、热盛动风、肝阳化风、肝肾虚风等出现眩晕抽掣之症，俱属肝风为患。脾恶湿，既反映了脾病每致湿浊内停的倾向，又说明脾湿形成或外感湿邪均会反过来阻碍脾气运化这一因果交替的病理特点。肾恶燥，乃因肾主水而藏精，凡津液精血的亏损都会导致肾阴干涸的内燥证。因此，"五脏所恶"有助于认识五脏病机的个性和特征。\n\n\n'
    ]

    start_time = time.time()
    # 注意：这里需要使用 asyncio.run() 来运行异步函数
    filter_index, filter_docs, summary_docs = asyncio.run(summary(input_text, judege_list))
    end_time = time.time()
    elapsed_time = end_time - start_time

    print(f"筛选时间：{elapsed_time}秒")
    print(f"筛选结果索引：{filter_index}")
    print(f"筛选结果文档数量：{len(filter_docs)}")
    print(f"总结文档数量：{len(summary_docs)}")