from enum import StrEnum
from json import loads
from typing import Annotated, Any, Dict
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):

    OPENAI_API_KEY: str="sk-8O7LaJ7d4YP8JuPN16Fa330664924811A10aF4Ad1eFeA1Cc"
    LLM_MODEL: str = "qwen-plus-latest"
    LLM_SMALL_MODEL: str = "qwen-turbo"
    EMBEDDING_MODEL: str = "text-embedding-v3"
    MODEL_API_BASE: str = "https://one-api.igancao.cn/v1"

    # Milvus配置
    MILVUS_URL: str = "http://c-083fddec0b90d74c.milvus.aliyuncs.com:19530"
    MILVUS_TOKEN: str = "root:GC2023!0814@&^$%#es"
    MILVUS_DB_NAME: str = "leeman"
    MILVUS_MAX_CONNECTIONS: int = 10
    MILVUS_CONNECTION_TIMEOUT: float = 30.0
    MILVUS_RETRY_ATTEMPTS: int = 3
    MILVUS_RETRY_DELAY: float = 1.0

    LLM_BIG_MODEL: str = "deepseek-r1"


settings = Settings()
