"""
改进的Milvus连接池管理器 - 修复内存泄露和并发安全问题
"""
import asyncio
import json
import time
import weakref
from contextlib import asynccontextmanager
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Set
import threading
from collections import deque

from pymilvus import MilvusClient
from loguru import logger

from app.config.settings import settings


@dataclass
class ConnectionInfo:
    """连接信息"""
    client: MilvusClient
    created_at: float
    last_used: float
    usage_count: int = 0
    is_healthy: bool = True
    last_health_check: float = field(default_factory=time.time)


class AsyncConnectionPool:
    """异步安全的连接池"""

    def __init__(
        self,
        max_connections: int = None,
        connection_timeout: float = None,
        retry_attempts: int = None,
        retry_delay: float = None,
        health_check_interval: float = 300.0  # 5分钟检查一次
    ):
        self.max_connections = max_connections or settings.MILVUS_MAX_CONNECTIONS
        self.connection_timeout = connection_timeout or settings.MILVUS_CONNECTION_TIMEOUT
        self.retry_attempts = retry_attempts or settings.MILVUS_RETRY_ATTEMPTS
        self.retry_delay = retry_delay or settings.MILVUS_RETRY_DELAY
        self.health_check_interval = health_check_interval

        # 使用 asyncio 原生的同步原语
        self._lock = asyncio.Lock()
        self._pool: deque[ConnectionInfo] = deque()
        self._active_connections: Dict[int, ConnectionInfo] = {}
        self._creating_connections: Set[asyncio.Task] = set()
        self._closed = False

        # 统计信息
        self._total_created = 0
        self._total_requests = 0
        self._connection_semaphore = asyncio.Semaphore(self.max_connections)

        # 弱引用跟踪，防止循环引用
        self._weak_refs: Set[weakref.ref] = set()

        logger.info(f"初始化异步Milvus连接池，最大连接数: {self.max_connections}")

    async def _create_connection(self, db_name: str = None) -> MilvusClient:
        """创建新的Milvus连接"""
        db = db_name or settings.MILVUS_DB_NAME

        for attempt in range(self.retry_attempts):
            try:
                # 在线程池中创建连接，避免阻塞事件循环
                client = await asyncio.get_event_loop().run_in_executor(
                    None,
                    self._sync_create_connection,
                    db
                )

                async with self._lock:
                    self._total_created += 1

                logger.debug(f"成功创建Milvus连接 (尝试 {attempt + 1}/{self.retry_attempts})")
                return client

            except Exception as e:
                logger.warning(f"创建Milvus连接失败 (尝试 {attempt + 1}/{self.retry_attempts}): {e}")
                if attempt < self.retry_attempts - 1:
                    await asyncio.sleep(self.retry_delay * (2 ** attempt))
                else:
                    raise ConnectionError(f"无法创建Milvus连接，已尝试 {self.retry_attempts} 次: {e}")

    def _sync_create_connection(self, db_name: str) -> MilvusClient:
        """同步创建连接（在线程池中执行）"""
        client = MilvusClient(
            uri=settings.MILVUS_URL,
            token=settings.MILVUS_TOKEN,
            db_name=db_name,
            timeout=self.connection_timeout
        )

        # 测试连接
        client.list_collections()
        return client

    async def _is_connection_healthy(self, connection_info: ConnectionInfo) -> bool:
        """检查连接是否健康（带缓存）"""
        current_time = time.time()

        # 如果最近检查过且连接标记为健康，跳过检查
        if (connection_info.is_healthy and
            current_time - connection_info.last_health_check < self.health_check_interval):
            return True

        try:
            # 在线程池中进行健康检查，避免阻塞
            await asyncio.get_event_loop().run_in_executor(
                None,
                connection_info.client.list_collections
            )
            connection_info.is_healthy = True
            connection_info.last_health_check = current_time
            return True
        except Exception as e:
            logger.warning(f"连接健康检查失败: {e}")
            connection_info.is_healthy = False
            return False

    @asynccontextmanager
    async def get_connection(self, db_name: str = None):
        """获取连接的异步上下文管理器"""
        if self._closed:
            raise RuntimeError("连接池已关闭")

        # 使用信号量限制并发连接数
        await self._connection_semaphore.acquire()
        connection_info = None

        try:
            async with self._lock:
                self._total_requests += 1

            # 尝试从池中获取健康连接
            connection_info = await self._get_or_create_connection(db_name)

            # 更新使用信息
            connection_info.last_used = time.time()
            connection_info.usage_count += 1

            # 记录活跃连接
            conn_id = id(connection_info.client)
            async with self._lock:
                self._active_connections[conn_id] = connection_info

            # 创建弱引用以跟踪连接
            weak_ref = weakref.ref(connection_info.client, self._cleanup_weak_ref)
            self._weak_refs.add(weak_ref)

            logger.debug(f"获取连接成功，连接ID: {conn_id}")
            yield connection_info.client

        except Exception as e:
            logger.error(f"获取连接失败: {e}")
            if connection_info:
                await self._safe_close_connection(connection_info.client)
            raise
        finally:
            # 归还连接到池中
            if connection_info:
                await self._return_connection(connection_info)

            self._connection_semaphore.release()

    async def _get_or_create_connection(self, db_name: str) -> ConnectionInfo:
        """获取或创建连接"""
        # 尝试从池中获取健康连接
        async with self._lock:
            while self._pool:
                connection_info = self._pool.popleft()
                if await self._is_connection_healthy(connection_info):
                    return connection_info
                else:
                    # 连接不健康，关闭它
                    await self._safe_close_connection(connection_info.client)

        # 池中没有可用连接，创建新连接
        client = await self._create_connection(db_name)
        return ConnectionInfo(
            client=client,
            created_at=time.time(),
            last_used=time.time()
        )

    async def _return_connection(self, connection_info: ConnectionInfo):
        """归还连接到池中"""
        conn_id = id(connection_info.client)

        async with self._lock:
            # 从活跃连接中移除
            self._active_connections.pop(conn_id, None)

            # 检查连接是否仍然健康且池未满
            if (not self._closed and
                await self._is_connection_healthy(connection_info) and
                len(self._pool) < self.max_connections):

                self._pool.append(connection_info)
                logger.debug(f"连接归还到池中，连接ID: {conn_id}")
            else:
                await self._safe_close_connection(connection_info.client)

    async def _safe_close_connection(self, client: MilvusClient):
        """安全关闭连接"""
        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                self._sync_close_connection,
                client
            )
        except Exception as e:
            logger.warning(f"关闭连接时出错: {e}")

    def _sync_close_connection(self, client: MilvusClient):
        """同步关闭连接"""
        try:
            if hasattr(client, 'close'):
                client.close()
        except Exception:
            pass

    def _cleanup_weak_ref(self, weak_ref):
        """清理弱引用"""
        self._weak_refs.discard(weak_ref)

    async def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        async with self._lock:
            return {
                "pool_size": len(self._pool),
                "active_connections": len(self._active_connections),
                "max_connections": self.max_connections,
                "total_created": self._total_created,
                "total_requests": self._total_requests,
                "is_closed": self._closed,
                "weak_refs_count": len(self._weak_refs)
            }

    async def close(self):
        """关闭连接池"""
        if self._closed:
            return

        logger.info("开始关闭Milvus连接池")

        async with self._lock:
            self._closed = True

            # 关闭池中的所有连接
            while self._pool:
                connection_info = self._pool.popleft()
                await self._safe_close_connection(connection_info.client)

            # 关闭活跃连接
            for connection_info in list(self._active_connections.values()):
                await self._safe_close_connection(connection_info.client)

            self._active_connections.clear()
            self._weak_refs.clear()

        logger.info("Milvus连接池已关闭")

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()


class MilvusManager:
    """改进的Milvus管理器 - 异步单例模式"""
    _instance = None
    _lock = asyncio.Lock()

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    async def __ainit__(self):
        """异步初始化"""
        if self._initialized:
            return

        async with self._lock:
            if not self._initialized:
                self.connection_pool = AsyncConnectionPool()
                self._initialized = True

                logger.info("异步Milvus管理器初始化完成")

    @asynccontextmanager
    async def get_client(self, db_name: str = None):
        """获取Milvus客户端"""
        if not self._initialized:
            await self.__ainit__()

        async with self.connection_pool.get_connection(db_name) as client:
            yield client

    async def search(
        self,
        vector: List[float],
        collection_name: str,
        book_list: List[str] = None,
        top_k: int = 3,
        output_fields: List[str] = None,
        is_array: bool = False,
        db_name: str = None
    ) -> List[Dict]:
        """执行向量搜索"""
        async with self.get_client(db_name) as client:
            # 构建过滤条件
            filter_cond = ''
            if book_list:
                if is_array:
                    filter_cond = f'array_contains_any(book, {json.dumps(book_list)})'
                else:
                    filter_cond = f'book in {json.dumps(book_list)}'

            try:
                # 在线程池中执行搜索，避免阻塞事件循环
                results = await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: client.search(
                        collection_name=collection_name,
                        data=[vector],
                        output_fields=output_fields,
                        limit=top_k,
                        filter=filter_cond
                    )
                )
                return results[0] if results else []
            except Exception as e:
                logger.error(f"搜索失败 - 集合: {collection_name}, 错误: {e}")
                raise

    async def query(
        self,
        collection_name: str,
        filter_expr: str,
        output_fields: List[str] = None,
        db_name: str = None
    ) -> List[Dict]:
        """执行查询"""
        async with self.get_client(db_name) as client:
            try:
                # 在线程池中执行查询
                results = await asyncio.get_event_loop().run_in_executor(
                    None,
                    lambda: client.query(
                        collection_name=collection_name,
                        filter=filter_expr,
                        output_fields=output_fields
                    )
                )
                return results
            except Exception as e:
                logger.error(f"查询失败 - 集合: {collection_name}, 错误: {e}")
                raise

    async def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self._initialized:
            return {"status": "not_initialized"}
        return await self.connection_pool.get_stats()

    async def close(self):
        """关闭管理器"""
        if hasattr(self, 'connection_pool') and self._initialized:
            await self.connection_pool.close()


# 全局实例
milvus_manager = MilvusManager()


async def get_milvus_manager() -> MilvusManager:
    """获取Milvus管理器实例"""
    if not milvus_manager._initialized:
        await milvus_manager.__ainit__()
    return milvus_manager