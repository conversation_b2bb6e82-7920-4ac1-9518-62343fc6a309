"""
Milvus连接池管理器 - 解决连接泄露和资源管理问题
"""
import asyncio
import json
import threading
import time
from contextlib import asynccontextmanager
from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from queue import Queue, Empty
import weakref

from pymilvus import MilvusClient, connections
from loguru import logger

from app.config.settings import settings


@dataclass
class ConnectionInfo:
    """连接信息"""
    client: MilvusClient
    created_at: float
    last_used: float
    usage_count: int = 0
    is_healthy: bool = True


class MilvusConnectionPool:
    """Milvus连接池"""
    
    def __init__(
        self,
        max_connections: int = None,
        connection_timeout: float = None,
        retry_attempts: int = None,
        retry_delay: float = None
    ):
        self.max_connections = max_connections or settings.MILVUS_MAX_CONNECTIONS
        self.connection_timeout = connection_timeout or settings.MILVUS_CONNECTION_TIMEOUT
        self.retry_attempts = retry_attempts or settings.MILVUS_RETRY_ATTEMPTS
        self.retry_delay = retry_delay or settings.MILVUS_RETRY_DELAY
        
        # 连接池
        self._pool: Queue[ConnectionInfo] = Queue(maxsize=self.max_connections)
        self._active_connections: Dict[int, ConnectionInfo] = {}
        self._lock = threading.RLock()
        self._closed = False
        
        # 统计信息
        self._total_created = 0
        self._total_requests = 0
        
        logger.info(f"初始化Milvus连接池，最大连接数: {self.max_connections}")

    def _create_connection(self, db_name: str = None) -> MilvusClient:
        """创建新的Milvus连接"""
        db = db_name or settings.MILVUS_DB_NAME
        
        for attempt in range(self.retry_attempts):
            try:
                client = MilvusClient(
                    uri=settings.MILVUS_URL,
                    token=settings.MILVUS_TOKEN,
                    db_name=db,
                    timeout=self.connection_timeout
                )
                
                # 测试连接
                client.list_collections()
                
                self._total_created += 1
                logger.debug(f"成功创建Milvus连接 (尝试 {attempt + 1}/{self.retry_attempts})")
                return client
                
            except Exception as e:
                logger.warning(f"创建Milvus连接失败 (尝试 {attempt + 1}/{self.retry_attempts}): {e}")
                if attempt < self.retry_attempts - 1:
                    time.sleep(self.retry_delay * (2 ** attempt))  # 指数退避
                else:
                    raise ConnectionError(f"无法创建Milvus连接，已尝试 {self.retry_attempts} 次: {e}")

    def _is_connection_healthy(self, client: MilvusClient) -> bool:
        """检查连接是否健康"""
        try:
            client.list_collections()
            return True
        except Exception as e:
            logger.warning(f"连接健康检查失败: {e}")
            return False

    @asynccontextmanager
    async def get_connection(self, db_name: str = None):
        """获取连接的异步上下文管理器"""
        if self._closed:
            raise RuntimeError("连接池已关闭")
            
        connection_info = None
        self._total_requests += 1
        
        try:
            # 尝试从池中获取连接
            with self._lock:
                try:
                    connection_info = self._pool.get_nowait()
                    # 检查连接是否健康
                    if not self._is_connection_healthy(connection_info.client):
                        logger.warning("从池中获取的连接不健康，创建新连接")
                        self._safe_close_connection(connection_info.client)
                        connection_info = None
                except Empty:
                    connection_info = None
            
            # 如果池中没有可用连接，创建新连接
            if connection_info is None:
                client = await asyncio.get_event_loop().run_in_executor(
                    None, self._create_connection, db_name
                )
                connection_info = ConnectionInfo(
                    client=client,
                    created_at=time.time(),
                    last_used=time.time()
                )
            
            # 更新使用信息
            connection_info.last_used = time.time()
            connection_info.usage_count += 1
            
            # 记录活跃连接
            conn_id = id(connection_info.client)
            with self._lock:
                self._active_connections[conn_id] = connection_info
            
            logger.debug(f"获取连接成功，连接ID: {conn_id}")
            yield connection_info.client
            
        except Exception as e:
            logger.error(f"获取连接失败: {e}")
            if connection_info:
                self._safe_close_connection(connection_info.client)
            raise
        finally:
            # 归还连接到池中
            if connection_info:
                conn_id = id(connection_info.client)
                with self._lock:
                    self._active_connections.pop(conn_id, None)
                    
                    # 检查连接是否仍然健康
                    if (not self._closed and 
                        self._is_connection_healthy(connection_info.client) and
                        self._pool.qsize() < self.max_connections):
                        try:
                            self._pool.put_nowait(connection_info)
                            logger.debug(f"连接归还到池中，连接ID: {conn_id}")
                        except:
                            self._safe_close_connection(connection_info.client)
                    else:
                        self._safe_close_connection(connection_info.client)

    def _safe_close_connection(self, client: MilvusClient):
        """安全关闭连接"""
        try:
            if hasattr(client, 'close'):
                client.close()
        except Exception as e:
            logger.warning(f"关闭连接时出错: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        with self._lock:
            return {
                "pool_size": self._pool.qsize(),
                "active_connections": len(self._active_connections),
                "max_connections": self.max_connections,
                "total_created": self._total_created,
                "total_requests": self._total_requests,
                "is_closed": self._closed
            }

    def close(self):
        """关闭连接池"""
        if self._closed:
            return
            
        logger.info("开始关闭Milvus连接池")
        self._closed = True
        
        with self._lock:
            # 关闭池中的所有连接
            while not self._pool.empty():
                try:
                    connection_info = self._pool.get_nowait()
                    self._safe_close_connection(connection_info.client)
                except Empty:
                    break
            
            # 关闭活跃连接
            for connection_info in self._active_connections.values():
                self._safe_close_connection(connection_info.client)
            
            self._active_connections.clear()
        
        logger.info("Milvus连接池已关闭")

    def __del__(self):
        """析构函数，确保资源清理"""
        if not self._closed:
            self.close()


class MilvusManager:
    """Milvus管理器 - 单例模式"""
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self.connection_pool = MilvusConnectionPool()
        self._initialized = True
        
        # 注册清理函数
        import atexit
        atexit.register(self.close)
        
        logger.info("Milvus管理器初始化完成")

    @asynccontextmanager
    async def get_client(self, db_name: str = None):
        """获取Milvus客户端"""
        async with self.connection_pool.get_connection(db_name) as client:
            yield client

    async def search(
        self, 
        vector: List[float], 
        collection_name: str,
        book_list: List[str] = None,
        top_k: int = 3,
        output_fields: List[str] = None,
        is_array: bool = False,
        db_name: str = None
    ) -> List[Dict]:
        """执行向量搜索"""
        async with self.get_client(db_name) as client:
            # 构建过滤条件
            filter_cond = ''
            if book_list:
                if is_array:
                    filter_cond = f'array_contains_any(book, {json.dumps(book_list)})'
                else:
                    filter_cond = f'book in {json.dumps(book_list)}'
            
            try:
                results = client.search(
                    collection_name=collection_name,
                    data=[vector],
                    output_fields=output_fields,
                    limit=top_k,
                    filter=filter_cond
                )
                return results[0] if results else []
            except Exception as e:
                logger.error(f"搜索失败 - 集合: {collection_name}, 错误: {e}")
                raise

    async def query(
        self,
        collection_name: str,
        filter_expr: str,
        output_fields: List[str] = None,
        db_name: str = None
    ) -> List[Dict]:
        """执行查询"""
        async with self.get_client(db_name) as client:
            try:
                results = client.query(
                    collection_name=collection_name,
                    filter=filter_expr,
                    output_fields=output_fields
                )
                return results
            except Exception as e:
                logger.error(f"查询失败 - 集合: {collection_name}, 错误: {e}")
                raise

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.connection_pool.get_stats()

    def close(self):
        """关闭管理器"""
        if hasattr(self, 'connection_pool'):
            self.connection_pool.close()


# 全局实例
milvus_manager = MilvusManager()


def get_milvus_manager() -> MilvusManager:
    """获取Milvus管理器实例"""
    return milvus_manager
