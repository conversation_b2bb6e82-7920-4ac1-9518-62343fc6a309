import cv2
import numpy as np
from ultralytics import YOLO
import time

def main():
    """简化版实时手部关键点检测"""
    
    # 加载训练好的模型
    print("正在加载模型...")
    model = YOLO("11x-best.pt")
    
    # 初始化摄像头
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("错误: 无法打开摄像头")
        return
    
    # 设置摄像头参数
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
    
    # 关键点名称（对应您的配置）
    kpt_names = [
        "zhongchong",  # 中冲穴
        "shaoze",      # 少泽穴
        "qiangu",      # 前谷穴
        "sifeng",      # 四缝穴
        "laogong",     # 劳宫穴
        "shaofu",      # 少府穴
        "wangu"        # 腕骨穴
    ]
    
    # 关键点颜色
    kpt_colors = [
        (0, 0, 255),     # 中冲 - 红色
        (0, 255, 0),     # 少泽 - 绿色
        (255, 0, 0),     # 前谷 - 蓝色
        (0, 255, 255),   # 四缝 - 黄色
        (255, 0, 255),   # 劳宫 - 洋红
        (255, 255, 0),   # 少府 - 青色
        (0, 128, 255)    # 腕骨 - 橙色
    ]
    
    print("开始检测... 按 'q' 退出")
    
    # FPS计算
    fps_counter = 0
    fps_start_time = time.time()
    current_fps = 0
    
    try:
        while True:
            # 读取摄像头帧
            ret, frame = cap.read()
            if not ret:
                print("无法读取摄像头数据")
                break
            
            # 水平翻转（镜像效果）
            frame = cv2.flip(frame, 1)
            
            # 进行检测
            results = model(frame, verbose=False)
            
            # 处理检测结果
            for result in results:
                # 绘制检测框
                if result.boxes is not None:
                    boxes = result.boxes.xyxy.cpu().numpy()
                    confidences = result.boxes.conf.cpu().numpy()
                    
                    for box, conf in zip(boxes, confidences):
                        x1, y1, x2, y2 = map(int, box)
                        cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                        cv2.putText(frame, f"Hand: {conf:.2f}", (x1, y1-10),
                                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
                
                # 绘制关键点
                if result.keypoints is not None:
                    keypoints = result.keypoints.xy.cpu().numpy()
                    kpt_confidences = result.keypoints.conf.cpu().numpy()
                    
                    for kpts, kpt_confs in zip(keypoints, kpt_confidences):
                        for i, ((x, y), conf) in enumerate(zip(kpts, kpt_confs)):
                            if conf > 0.5:  # 置信度阈值
                                px, py = int(x), int(y)
                                
                                # 绘制关键点
                                color = kpt_colors[i % len(kpt_colors)]
                                cv2.circle(frame, (px, py), 5, color, -1)
                                cv2.circle(frame, (px, py), 7, (255, 255, 255), 2)
                                
                                # 绘制关键点名称
                                cv2.putText(frame, kpt_names[i], (px-20, py-10),
                                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            
            # 计算并显示FPS
            fps_counter += 1
            current_time = time.time()
            if current_time - fps_start_time >= 1.0:
                current_fps = fps_counter / (current_time - fps_start_time)
                fps_counter = 0
                fps_start_time = current_time
            
            cv2.putText(frame, f"FPS: {current_fps:.1f}", (10, 30),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
            
            # 显示结果
            cv2.imshow('Hand Keypoint Detection', frame)
            
            # 按 'q' 退出
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
    
    except KeyboardInterrupt:
        print("\n检测被用户中断")
    
    finally:
        # 清理资源
        cap.release()
        cv2.destroyAllWindows()
        print("检测结束")

if __name__ == "__main__":
    main()
