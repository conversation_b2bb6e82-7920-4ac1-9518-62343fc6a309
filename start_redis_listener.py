#!/usr/bin/env python
# -*- coding: utf-8 -*-
import argparse
import os
import sys
from loguru import logger

# 确保工作目录是项目根目录
script_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, script_dir)

from utils.redis_listener import MyRedisListener, redis_listener_context


def start_listener(redis_db, mode, target, interval):
    """
    启动Redis监听器
    
    Args:
        redis_db: Redis数据库编号
        mode: 监听模式 ('pubsub'或'key')
        target: 监听目标 (频道名称或键模式)
        interval: 轮询间隔（秒）
    """
    # 设置日志
    log_file = os.path.join(script_dir, "logs", "redis_listener.log")
    os.makedirs(os.path.dirname(log_file), exist_ok=True)
    logger.add(log_file, rotation="500 MB", level="INFO")
    
    logger.info(f"启动Redis监听器: 数据库={redis_db}, 模式={mode}, 目标={target}, 间隔={interval}秒")
    
    if mode == 'pubsub':
        listener = MyRedisListener(redis_db=redis_db, channel=target, sleep_interval=interval)
    else:  # mode == 'key'
        listener = MyRedisListener(redis_db=redis_db, key_pattern=target, sleep_interval=interval)
    
    # 使用上下文管理器启动监听
    with redis_listener_context(listener):
        logger.info("Redis监听器已启动")
        try:
            # 保持进程运行
            import time
            while True:
                time.sleep(60)
        except KeyboardInterrupt:
            logger.info("接收到中断信号，正在退出...")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='启动Redis监听器')
    parser.add_argument('--db', type=int, default=0, help='Redis数据库编号')
    parser.add_argument('--mode', choices=['pubsub', 'key'], default='pubsub', help='监听模式: pubsub(发布/订阅) 或 key(键监听)')
    parser.add_argument('--target', required=True, help='要监听的目标 (频道名称或键模式)')
    parser.add_argument('--interval', type=float, default=1.0, help='轮询间隔（秒）')
    
    args = parser.parse_args()
    
    # 设置当前环境变量
    if 'CUR_ENV' not in os.environ:
        os.environ['CUR_ENV'] = 'test'  # 默认使用测试环境
    
    start_listener(args.db, args.mode, args.target, args.interval) 